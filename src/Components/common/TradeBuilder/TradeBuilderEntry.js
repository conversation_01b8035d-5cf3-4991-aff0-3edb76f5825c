import { useState, useEffect, useRef, useMemo } from "react";
import { Col, Row } from "react-bootstrap";
import {
  DropArrowIcon,
  SolidInfoIcon,
  PlusIcon,
  DropArrowUpIcon,
  MinusIcon,
} from "@/assets/svgIcons/SvgIcon";
import AdminHeading from "@/Components/common/Dashboard/AdminHeading";
import CommonTooltip from "@/Components/UI/CommonTooltip";
import { post, get } from "@/utils/apiUtils";
import CommonButton from "@/Components/UI/CommonButton";
import ConfigScopeModal from "./ConfigScopeModal";

export default function TradeBuilderEntry({
  formKey,
  formData,
  index,
  setFormData,
  transactionFields,
  tradeFields,
  portfolioFields,
  updateDatabasePayload,
  tradePublishStatus = "draft",
  tradeId,
  onDeleteForm,
  onSaveStatusChange,
  onFormPublish,
  selectedTradeAccountId,
  limitPerSubscription,
  planName,
  formId,
  formKeyMap
}) {
  const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;
  const [inputValues, setInputValues] = useState({});
  const [lockedFields, setLockedFields] = useState({});
  const [touchedFields, setTouchedFields] = useState({});
  const [projectionValues, setProjectionValues] = useState({});
  const [outcomeValues, setOutcomeValues] = useState({});
  const [blurredFields, setBlurredFields] = useState({});
  const [isDimentionModal, setIsDimentionModal] = useState(false);
  const [entryIsOpen, setEntryIsOpen] = useState(false);
  const [height, setHeight] = useState("0px");
  const contentRef = useRef(null);
  const [activeSection, setActiveSection] = useState("overview");
  const [isFocused, setIsFocused] = useState({});
  const [inputMode, setInputMode] = useState({
    transaction_risk_percentage: false,
    transaction_quantity_purchased: true,
  });
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pendingToggleField, setPendingToggleField] = useState(null);
  const [notesData, setNotesData] = useState("");
  const [lastSavedNote, setLastSavedNote] = useState("");
  const [updatedCalculatedFields, setUpdatedCalculatedFields] = useState({});
  const [lastCalculatedValues, setLastCalculatedValues] = useState({});
  const [liveSelectedFields, setLiveSelectedFields] = useState({
    overview: [],
    projection: [],
    outcome: [],
  });
  const [selectedExtraFields, setSelectedExtraFields] = useState({
    overview: [],
    projection: [],
    outcome: [],
  });
  const [defaultFields, setDefaultFields] = useState({
    overview: [],
    projection: [],
    outcome: [],
  });
  const [rawInputs, setRawInputs] = useState({});
  const [publishStatus, setPublishStatus] = useState("draft");
  const fileInputRef = useRef(null);
  const debounceRef = useRef(null);
  const notesDebounceRef = useRef(null);
  const hasAutoCalculated = useRef(false);
  const [chartImageUrl, setChartImageUrl] = useState("");
  const [uploadError, setUploadError] = useState("");
  const [newlyAddedFields, setNewlyAddedFields] = useState(new Set());
  const [extraFieldCount, setExtraFieldCount] = useState({
    overview: 0,
    projection: 0,
    outcome: 0,
  });
  const [modalErrorMessage, setModalErrorMessage] = useState("");
  const overviewList = Array.isArray(formData[formKey]?.overview) ? formData[formKey].overview : [];
  const entryProjectionList = Array.isArray(formData[formKey]?.projection) ? formData[formKey].projection : [];
  const entryOutcomeList = Array.isArray(formData[formKey]?.outcome) ? formData[formKey].outcome : [];
  const formulaModeInputs = formData[formKey]?.formulaModeInputs || {};
  const initializedRef = useRef(false);

  const openDimentionModal = () => setIsDimentionModal(true);
  const closeDimentionModal = () => setIsDimentionModal(false);

  const toggleEntryCollapse = () => {
    setEntryIsOpen((prev) => !prev);
  };

  useEffect(() => {
    if (entryIsOpen) {
      setHeight("auto");
    } else {
      setHeight("0px");
    }
  }, [entryIsOpen]);

  useEffect(() => {
    if (initializedRef.current) return;

    const overviewExtraItems = (overviewList || []).filter(item => item.is_extra);
    const projectionExtraItems = (entryProjectionList || []).filter(item => item.is_extra);
    const outcomeExtraItems = (entryOutcomeList || []).filter(item => item.is_extra);

    const overviewDefaults = overviewList.filter(item => !item.is_extra).map(item => item.input || item.database_field);
    const projectionDefaults = entryProjectionList.filter(item => !item.is_extra).map(item => item.input || item.database_field);
    const outcomeDefaults = entryOutcomeList.filter(item => !item.is_extra).map(item => item.input || item.database_field);

    setExtraFieldCount({
      overview: overviewExtraItems.length,
      projection: projectionExtraItems.length,
      outcome: outcomeExtraItems.length,
    });

    setSelectedExtraFields({
      overview: [...new Set(overviewExtraItems.map(f => f.input || f.database_field))],
      projection: [...new Set(projectionExtraItems.map(f => f.input || f.database_field))],
      outcome: [...new Set(outcomeExtraItems.map(f => f.input || f.database_field))],
    });

    setLiveSelectedFields({
      overview: [...new Set(overviewExtraItems.map(f => f.input || f.database_field))],
      projection: [...new Set(projectionExtraItems.map(f => f.input || f.database_field))],
      outcome: [...new Set(outcomeExtraItems.map(f => f.input || f.database_field))],
    });

    setDefaultFields({
      overview: [...new Set(overviewDefaults)],
      projection: [...new Set(projectionDefaults)],
      outcome: [...new Set(outcomeDefaults)]
    });

    initializedRef.current = true;
    
  }, [overviewList, entryProjectionList, entryOutcomeList]);

  useEffect(() => {
    const protectedFields = ["transaction_risk_percentage", "transaction_quantity_purchased"];

    const initialBlurredFields = {};
    const initialInputValues = {};
    const initialLockedFields = {};
    const initialTouchedFields = {};
    const initialProjectionValues = {};
    const initialOutcomeValues = {};
    const initialInputMode = { ...inputMode };
    const initialIsFocused = {};

    overviewList.forEach((item) => {
      initialBlurredFields[item.input] = true;
      initialInputValues[item.input] = item?.portfolioValue || "";
      initialLockedFields[item.input] = publishStatus === "published";
      initialTouchedFields[item.input] = false;
      initialIsFocused[item.input] = false;
      if (!protectedFields.includes(item.input)) {
        initialInputMode[item.input] = formulaModeInputs[item.input] === true ? false : (item.is_editable || false);
      }
    });

    entryProjectionList.forEach((item) => {
      initialBlurredFields[item.input] = true;
      initialProjectionValues[item.input] = item?.portfolioValue || "";
      initialLockedFields[item.input] = publishStatus === "published";
      initialTouchedFields[item.input] = false;
      initialIsFocused[item.input] = false;
      if (!protectedFields.includes(item.input)) {
        initialInputMode[item.input] = formulaModeInputs[item.input] === true ? false : (item.is_editable || false);
      }
    });

    entryOutcomeList.forEach((item) => {
      initialBlurredFields[item.input] = true;
      initialOutcomeValues[item.input] = item?.portfolioValue || "";
      initialLockedFields[item.input] = publishStatus === "published";
      initialTouchedFields[item.input] = false;
      initialIsFocused[item.input] = false;
      if (!protectedFields.includes(item.input)) {
        initialInputMode[item.input] = formulaModeInputs[item.input] === true ? false : (item.is_editable || false);
      }
    });

    if (protectedFields.includes("transaction_risk_percentage") && protectedFields.includes("transaction_quantity_purchased")) {
      if (formulaModeInputs.transaction_risk_percentage === true) {
        initialInputMode.transaction_risk_percentage = false;
        initialInputMode.transaction_quantity_purchased = true;
      } else if (formulaModeInputs.transaction_quantity_purchased === true) {
        initialInputMode.transaction_quantity_purchased = false;
        initialInputMode.transaction_risk_percentage = true;
      } else {
        initialInputMode.transaction_risk_percentage = false;
        initialInputMode.transaction_quantity_purchased = true;
      }
    }

    const setInitialId = (field, value) => {
      if (overviewList.some(item => item.input === field) && (!initialInputValues[field] || initialInputValues[field] === "")) {
        initialInputValues[field] = value;
        initialLockedFields[field] = true;
        initialTouchedFields[field] = false;
        initialBlurredFields[field] = true;
        initialIsFocused[field] = false;
        initialInputMode[field] = true;
      }
    };

    setInitialId("transaction_id", formId.toString());
    setInitialId("trade_id", tradeId.toString());
    setInitialId("portfolio_id", selectedTradeAccountId.toString());

    setBlurredFields(initialBlurredFields);
    setInputValues(initialInputValues);
    setLockedFields(initialLockedFields);
    setTouchedFields(initialTouchedFields);
    setProjectionValues(initialProjectionValues);
    setOutcomeValues(initialOutcomeValues);
    setInputMode(initialInputMode);
    setIsFocused(initialIsFocused);

    const notesItem = overviewList.find((item) => item.input === "transaction_comments");
    if (notesItem?.portfolioValue) {
      setNotesData(notesItem.portfolioValue);
      setLastSavedNote(notesItem.portfolioValue);
    }
  }, [formKey, overviewList, entryProjectionList, entryOutcomeList, publishStatus]);

  useEffect(() => {
    if (hasAutoCalculated.current || !formKey || formKey === "entry_undefined") return;

    const doInitialAutoCalculation = async () => {
      const overview = formData[formKey]?.overview || [];
      const projection = formData[formKey]?.projection || [];
      const outcome = formData[formKey]?.outcome || [];
      const formulaInputs = formData[formKey]?.formulaModeInputs || {};

      const allFields = [...overview, ...projection, ...outcome];

      const formulaModeMap = {};
      Object.entries(formulaInputs).forEach(([key, isFormula]) => {
        formulaModeMap[key] = isFormula === true;
      });

      console.log(allFields);
      const changedFields = allFields
        .filter((f) => f.is_editable == true && f.account_field !== 'YES' && f.portfolioValue)
        .map((f) => f.input);

      if (changedFields.length === 0) return;

      const allInputs = {
        ...Object.fromEntries(overview.map((f) => [f.input, f.portfolioValue])),
        ...Object.fromEntries(projection.map((f) => [f.input, f.portfolioValue])),
        ...Object.fromEntries(outcome.map((f) => [f.input, f.portfolioValue])),
      };

      try {
        const response = await post("/trade/calculate", {
          formKey: formKey,
          inputs: allInputs,
          locked: lockedFields,
          changedFields,
          formulaModeInputs: formulaModeMap,
        });

        const updated = {};
        Object.entries(response.calculated || {}).forEach(([key, value]) => {
          updated[key.toLowerCase()] = value;
        });

        const updateSection = (section) =>
          section.map((item) =>
            updated[item.input] !== undefined
              ? { ...item, portfolioValue: updated[item.input] }
              : item
          );

        setFormData((prev) => ({
          ...prev,
          [formKey]: {
            ...prev[formKey],
            overview: updateSection(overview),
            projection: updateSection(projection),
            outcome: updateSection(outcome),
          },
        }));

        setInputValues((prev) => ({ ...prev, ...updated }));
        setProjectionValues((prev) => ({ ...prev, ...updated }));
        setOutcomeValues((prev) => ({ ...prev, ...updated }));

        setRawInputs((prev) => {
          const copy = { ...prev };
          Object.keys(updated).forEach((key) => {
            if (copy[key]) delete copy[key];
          });
          return copy;
        });

        hasAutoCalculated.current = true;
      } catch (err) {
        console.error("Auto-calc error:", err.response?.data || err.message);
      }
    };

    doInitialAutoCalculation();
  }, []);

  useEffect(() => {
    if (formKey === 'entry_undefined') {
      return;
    }
    const fetchFormStatus = async () => {
      try {
        const response = await get(`/trade/form-status/${formKey}`);
        setPublishStatus(response.is_published ? "published" : "draft");
        if (response.is_published) {
          setLockedFields((prev) => {
            const newLocks = { ...prev };
            [...overviewList, ...entryProjectionList, ...entryOutcomeList].forEach((item) => {
              newLocks[item.input] = true;
            });
            return newLocks;
          });
        }
      } catch (error) {
        console.error(`Error fetching form status for formKey ${formKey}:`, error.response?.data || error.message);
      }
    };
    fetchFormStatus();
  }, []);

  const toggleLock = (field) => {
    setLockedFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const confirmToggleInputMode = (field) => {
    const isSpecial =
      field === "transaction_risk_percentage" ||
      field === "transaction_quantity_purchased";

    if (!isSpecial) {
      toggleInputMode(field);
    } else {
      setPendingToggleField(field);
      setShowConfirmModal(true);
    }
  };

  const handleConfirmToggle = () => {
    if (pendingToggleField) {
      toggleInputMode(pendingToggleField);
      setPendingToggleField(null);
      setShowConfirmModal(false);
    }
  };

  const handleCancelToggle = () => {
    setPendingToggleField(null);
    setShowConfirmModal(false);
  };

  const toggleInputMode = (field) => {
    setInputMode((prev) => {
      const isCurrentlyManual = prev[field];
      const newValue = !isCurrentlyManual;

      const updated = {
        ...prev,
        [field]: newValue,
      };

      if (field === "transaction_risk_percentage") {
        updated["transaction_quantity_purchased"] = !newValue;
      } else if (field === "transaction_quantity_purchased") {
        updated["transaction_risk_percentage"] = !newValue;
      }

      setFormData((prevFormData) => ({
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          formulaModeInputs: {
            ...prevFormData[formKey]?.formulaModeInputs || {},
            [field]: newValue === false, // true if formula mode
            ...(field === "transaction_risk_percentage" || field === "transaction_quantity_purchased"
              ? {
                [field === "transaction_risk_percentage" ? "transaction_quantity_purchased" : "transaction_risk_percentage"]:
                  !newValue === false,
              }
              : {}),
          },
        },
      }));

      saveToggle(updated, field, newValue);

      return updated;
    });
  };

  const saveToggle = async (updatedInputMode, field, newInputModeValue) => {
    if (publishStatus === "published") return;

    const changedFields = [field];
    if (field === "transaction_risk_percentage" || field === "transaction_quantity_purchased") {
      changedFields.push(
        field === "transaction_risk_percentage" ? "transaction_quantity_purchased" : "transaction_risk_percentage"
      );
    }

    const sections = {};
    changedFields.forEach((f) => {
      if (overviewList.some((item) => item.input === f)) sections[f] = "overview";
      else if (entryProjectionList.some((item) => item.input === f)) sections[f] = "projection";
      else if (entryOutcomeList.some((item) => item.input === f)) sections[f] = "outcome";
    });

    const uniqueSections = [...new Set(Object.values(sections))];
    if (onSaveStatusChange) onSaveStatusChange("loading");

    try {
      for (const sec of uniqueSections) {
        const dataForSection = changedFields
          .filter((f) => sections[f] === sec)
          .map((f) => {
            let value;
            if (sec === "overview") value = inputValues[f] || "";
            else if (sec === "projection") value = projectionValues[f] || "";
            else if (sec === "outcome") value = outcomeValues[f] || "";
            return { input: f, value, isFormula: updatedInputMode[f] === false };
          });

        const formulaModeMap = {};
        Object.entries(updatedInputMode).forEach(([k, v]) => {
          formulaModeMap[k] = v === false;
        });

        await post("/trade/save-section", {
          formKey,
          section: sec,
          data: dataForSection,
          formulaModeInputs: formulaModeMap,
          trade_account_id: selectedTradeAccountId,
        });

        if (newInputModeValue === false) {
          await updateField(field, null, true);
        }
      }
      if (onSaveStatusChange) onSaveStatusChange("success");
    } catch (err) {
      console.error("Save toggle error:", err.response?.data || err.message);
      if (onSaveStatusChange) onSaveStatusChange("error");
    }
  };

  const formatValue = (value, datatype, isBlurred, isFocused) => {
    const lowerType = datatype?.toLowerCase() || "";
    const isEmpty = value === null || value === undefined || value === "";

    if (isEmpty) return "";

    const date = new Date(value);
    const validDate = !isNaN(date.getTime());

    if (lowerType.includes("iso 8601")) {
      return validDate
        ? !isFocused
          ? date.toLocaleString("en-US", {
            month: "long",
            day: "numeric",
            year: "numeric",
            hour: "numeric",
            minute: "2-digit",
            second: "2-digit",
            hour12: true,
            timeZoneName: "short",
          })
          : date.toISOString()
        : value;
    }

    if (lowerType.includes("date") && lowerType.includes("hh")) {
      return validDate
        ? !isFocused
          ? date.toLocaleString("en-US", {
            month: "long",
            day: "numeric",
            year: "numeric",
            hour: "numeric",
            hour12: true,
          })
          : date.toISOString().slice(0, 16).replace("T", " ")
        : value;
    }

    if (lowerType.includes("date") && !lowerType.includes("hh")) {
      return validDate
        ? !isFocused
          ? date.toLocaleDateString("en-US", {
            month: "long",
            day: "numeric",
            year: "numeric",
          })
          : date.toISOString().slice(0, 10)
        : value;
    }

    if (lowerType.includes("time")) {
      return validDate ? date.toISOString().slice(11, 19) : value;
    }

    const num = parseFloat(value);
    const validNum = !isNaN(num) ? num : 0;

    if (!lowerType || isFocused) return value;

    const formatClean = (n) => Number(n).toString();
    const padNumber = (n, length) => String(Math.floor(Number(n))).padStart(length, "0");

    if (lowerType.includes("number (2-digit")) {
      return padNumber(validNum, 2);
    }

    if (lowerType.includes("number (4-digit")) {
      return padNumber(validNum, 4);
    }

    if (lowerType.includes("number (integer")) {
      return formatClean(validNum);
    }

    if (lowerType.includes("number (percentage)")) {
      return `${formatClean(validNum)}%`;
    }

    if (lowerType.includes("number (ratio)")) {
      return validNum.toFixed(2);
    }

    if (lowerType.includes("text (ratio")) {
      return `${validNum.toFixed(2)}X`;
    }

    if (
      lowerType.includes("currency") ||
      lowerType.includes("usd") ||
      lowerType.includes("dollar") ||
      lowerType.includes("$")
    ) {
      return `$${formatClean(validNum)}`;
    }

    if (lowerType.includes("text")) {
      return value;
    }

    if (lowerType.includes("number")) {
      return formatClean(validNum);
    }

    return value;
  };

  const handleBlur = (field) => {
    setIsFocused((prev) => ({
      ...prev,
      [field]: false,
    }));
    setBlurredFields((prev) => ({
      ...prev,
      [field]: true,
    }));

    if (rawInputs[field] !== undefined) {
      clearTimeout(debounceRef.current);
      updateField(field, rawInputs[field]);
    }
  };

  const handleFocus = (field) => {
    setIsFocused((prev) => ({
      ...prev,
      [field]: true,
    }));
    setBlurredFields((prev) => ({
      ...prev,
      [field]: false,
    }));
  };

  const saveNotes = async (notes) => {
    if (onSaveStatusChange) {
      onSaveStatusChange("loading");
    }
    try {
      const response = await post("/trade/save-section", {
        formKey,
        section: "overview",
        data: [{ input: "transaction_comments", value: notes, isFormula: false }],
        trade_account_id: selectedTradeAccountId
      });
      setLastSavedNote(notes);
      updateDatabasePayload(formKey, {
        section: "overview",
        data: { TRANSACTION_COMMENTS: notes },
      });
      if (onSaveStatusChange) {
        onSaveStatusChange("success");
      }
    } catch (error) {
      console.error("Error saving notes:", error.response?.data || error.message);
      if (onSaveStatusChange) {
        onSaveStatusChange("error");
      }
    }
  };

  const handleNotesChange = (e) => {
    const input = e.target.value;
    const allowedCharactersRegex = /^[a-zA-Z0-9\s.,:;!?'"()\-\n\r]*$/;

    if (allowedCharactersRegex.test(input)) {
      setNotesData(input);
      clearTimeout(notesDebounceRef.current);
      notesDebounceRef.current = setTimeout(() => {
        saveNotes(input);
      }, 500);
    }
  };

  const getRemaining = (section) => {
    return limitPerSubscription - (selectedExtraFields[section]?.length || 0);
  };

  const handleOpenModal = (section) => {
    setActiveSection(section);
    openDimentionModal();
  };

  const handleUpdateSelectedFields = async (fields) => {
    setLiveSelectedFields((prev) => ({
      ...prev,
      [activeSection]: fields.map(f => f.database_field),
    }));
  };

  const handleAddFields = async (newFields) => {
    if (Number(liveSelectedFields[activeSection]?.length) > Number(limitPerSubscription)) {
      setModalErrorMessage("You have exceeded the remaining limit for this section.");
      return false;
    }
    setModalErrorMessage("");

    const previousFields = new Set(selectedExtraFields[activeSection] || []);
    const currentFields = new Set(newFields.map(f => f.database_field));
    const removedFields = [...previousFields].filter(f => !currentFields.has(f));

    if (removedFields.length > 0) {
      const existingList = formData[formKey]?.[activeSection] || [];

      const removedFieldObjects = existingList.filter((item) =>
        removedFields.includes(item.input || item.database_field)
      );

      for (const item of removedFieldObjects) {
        await handleMinusClick(item, activeSection);
      }
    }

    const transformList = (list) =>
      list.map(
        ({
          field_name: title,
          summary: tooltip,
          database_field: input,
          account_field,
          portfolioValue,
          is_editable,
          datatype,
          metric_dimension,
          expected_values,
          has_formula,
        }) => {
          const existingField = [
            ...formData[formKey]?.overview || [],
            ...formData[formKey]?.projection || [],
            ...formData[formKey]?.outcome || [],
          ].find((item) => item.input === input);

          const existingValue = existingField?.portfolioValue;

          return {
            title,
            tooltip,
            input,
            account_field,
            portfolioValue:
              existingValue !== undefined
                ? existingValue
                : lastCalculatedValues[input] !== undefined
                  ? lastCalculatedValues[input]
                  : updatedCalculatedFields[input.toUpperCase()] !== undefined
                    ? updatedCalculatedFields[input.toUpperCase()]
                    : updatedCalculatedFields[input.toLowerCase()] !== undefined
                      ? updatedCalculatedFields[input.toLowerCase()]
                      : portfolioValue ?? "",
            is_editable: publishStatus === "published" ? false : (is_editable ?? true),
            datatype,
            metric_dimension,
            show_icon: true,
            expected_values: Array.isArray(expected_values) ? expected_values : [],
            has_formula,
          };
        }
      );

    const transformedNewFields = transformList(newFields);

    setFormData((prevFormData) => {
      const currentList = Array.isArray(prevFormData[formKey]?.[activeSection])
        ? prevFormData[formKey][activeSection]
        : [];
      const existingInputs = new Set(currentList.map((item) => item.input || item.database_field));
      const uniqueFields = transformedNewFields.filter((field) => !existingInputs.has(field.input));

      return {
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          [activeSection]: [...currentList, ...uniqueFields],
        },
      };
    });

    setSelectedExtraFields((prev) => ({
      ...prev,
      [activeSection]: [
        ...(prev?.[activeSection] || []),
        ...newFields.map((f) => f.database_field),
      ],
    }));

    setLiveSelectedFields((prev) => ({
      ...prev,
      [activeSection]: [
        ...(prev?.[activeSection] || []),
        ...newFields.map((f) => f.database_field),
      ],
    }));

    setExtraFieldCount((prev) => ({
      ...prev,
      [activeSection]: prev[activeSection] + newFields.length,
    }));

    const newFieldData = [];
    newFields.forEach((newField) => {
      const existingField = [
        ...formData[formKey]?.overview || [],
        ...formData[formKey]?.projection || [],
        ...formData[formKey]?.outcome || [],
      ].find((item) => item.input === newField?.database_field);

      const value =
        existingField?.portfolioValue !== undefined
          ? existingField.portfolioValue
          : lastCalculatedValues[newField?.database_field] !== undefined
            ? lastCalculatedValues[newField?.database_field]
            : updatedCalculatedFields[newField?.database_field.toUpperCase()] !== undefined
              ? updatedCalculatedFields[newField?.database_field.toUpperCase()]
              : updatedCalculatedFields[newField?.database_field.toLowerCase()] !== undefined
                ? updatedCalculatedFields[newField?.database_field.toLowerCase()]
                : newField?.portfolioValue || "";

      newFieldData.push({
        input: newField?.database_field,
        value: value,
        isFormula: newField?.has_formula,
        is_extra: true
      });
    });

    setNewlyAddedFields((prev) => {
      const updated = new Set(prev);
      newFields.forEach(({ database_field }) => updated.add(database_field));
      return updated;
    });

    try {
      await post("/trade/save-section", {
        formKey,
        section: activeSection,
        data: newFieldData,
        formulaModeInputs: Object.fromEntries(
          newFields.map(({ database_field, has_formula }) => [database_field, has_formula])
        ),
        trade_account_id: selectedTradeAccountId
      });
    } catch (error) {
      console.error("Error saving new fields:", error.response?.data || error.message);
      alert("Failed to save new fields.");
    }

    newFields.forEach(({ database_field, is_editable, portfolioValue, has_formula }) => {
      setInputMode((prev) => ({
        ...prev,
        [database_field]: publishStatus === "published" ? false : !has_formula,
      }));

      setLockedFields((prev) => ({
        ...prev,
        [database_field]: publishStatus === "published" ? true : false,
      }));
      setTouchedFields((prev) => ({
        ...prev,
        [database_field]: false,
      }));
      setBlurredFields((prev) => ({
        ...prev,
        [database_field]: true,
      }));

      const existingField = [
        ...formData[formKey]?.overview || [],
        ...formData[formKey]?.projection || [],
        ...formData[formKey]?.outcome || [],
      ].find((item) => item.input === database_field);

      const value =
        existingField?.portfolioValue !== undefined
          ? existingField.portfolioValue
          : lastCalculatedValues[database_field] !== undefined
            ? lastCalculatedValues[database_field]
            : updatedCalculatedFields[database_field.toUpperCase()] !== undefined
              ? updatedCalculatedFields[database_field.toUpperCase()]
              : updatedCalculatedFields[database_field.toLowerCase()] !== undefined
                ? updatedCalculatedFields[database_field.toLowerCase()]
                : portfolioValue || "";

      if (activeSection === "overview") {
        setInputValues((prev) => ({
          ...prev,
          [database_field]: value,
        }));
      } else if (activeSection === "projection") {
        setProjectionValues((prev) => ({
          ...prev,
          [database_field]: value,
        }));
      } else if (activeSection === "outcome") {
        setOutcomeValues((prev) => ({
          ...prev,
          [database_field]: value,
        }));
      }

      if (publishStatus === "draft") {
        updateDatabasePayload(formKey, {
          section: activeSection,
          data: { [database_field.toUpperCase()]: value },
        });
      }
    });

    const allInputs = {
      ...inputValues,
      ...projectionValues,
      ...outcomeValues,
    };

    const formulaModeMap = Object.fromEntries(
      Object.entries(inputMode).map(([k, v]) => [k, v === false])
    );

    newFields.forEach(({ database_field }) => {
      if (!(database_field in formulaModeMap)) {
        formulaModeMap[database_field] = inputMode[database_field] === false;
      }
    });

    const changedFields = newFields.map(f => f.database_field);

    try {
      const response = await post("/trade/calculate", {
        formKey: formKey,
        inputs: allInputs,
        locked: lockedFields,
        changedFields,
        formulaModeInputs: formulaModeMap,
      });

      const updated = {};
      Object.entries(response.calculated || {}).forEach(([key, value]) => {
        updated[key.toLowerCase()] = value;
      });

      setUpdatedCalculatedFields((prev) => ({ ...prev, ...response.calculated }));

      setInputValues((prev) => ({ ...prev, ...updated }));
      setProjectionValues((prev) => ({ ...prev, ...updated }));
      setOutcomeValues((prev) => ({ ...prev, ...updated }));

      setFormData((prev) => {
        const updateSection = (section) =>
          section.map((item) => ({
            ...item,
            portfolioValue: updated[item.input] !== undefined ? updated[item.input] : item.portfolioValue,
          }));

        return {
          ...prev,
          [formKey]: {
            ...prev[formKey],
            overview: updateSection(prev[formKey]?.overview || []),
            projection: updateSection(prev[formKey]?.projection || []),
            outcome: updateSection(prev[formKey]?.outcome || []),
          },
        };
      });

      if (publishStatus === "draft") {
        updateDatabasePayload(formKey, {
          section: activeSection,
          data: Object.fromEntries(
            Object.entries(updated)
              .filter(([k]) => {
                if (activeSection === "overview") return overviewList.some(item => item.input === k);
                if (activeSection === "projection") return entryProjectionList.some(item => item.input === k);
                if (activeSection === "outcome") return entryOutcomeList.some(item => item.input === k);
                return false;
              })
              .map(([k, v]) => [k.toUpperCase(), v])
          ),
        });
      }
    } catch (err) {
      console.error("Recalculation error after adding fields:", err.response?.data || err.message);
    }

    return true;
  };

  useEffect(() => {
    const notesItem = overviewList.find((item) => item.input === "transaction_comments");
    if (notesItem?.portfolioValue) {
      setNotesData(notesItem.portfolioValue);
      setLastSavedNote(notesItem.portfolioValue);
    }
  }, [formKey, overviewList]);

  const handleInputChange = (e, databaseField, expectedValues = [], datatype) => {
    let newValue = e.target.value;

    const lowerType = datatype?.toLowerCase() || "";
    if (lowerType.includes("date") && !lowerType.includes("hh")) {
      newValue = newValue;
    } else if (lowerType.includes("time")) {
      newValue = newValue;
    } else if (lowerType.includes("date") && lowerType.includes("hh")) {
      newValue = newValue.replace("T", " ") + ":00";
    }

    setRawInputs((prev) => ({
      ...prev,
      [databaseField]: newValue,
    }));

    const isTextField = lowerType.includes("text") || lowerType.includes("text (ratio") || lowerType.includes("alphanumeric");
    const isDateOrTime = lowerType.includes("date") || lowerType.includes("time");
    if (!isTextField && !isDateOrTime && expectedValues.length === 0 && newValue !== "" && !/^\d+(\.\d*)?$/.test(newValue)) {
      return;
    }

    clearTimeout(debounceRef.current);
    debounceRef.current = setTimeout(() => {
      updateField(databaseField, newValue);
    }, 500);
  };

  const updateField = async (databaseField, newValue, isCalculation = false) => {
    if (publishStatus === "published" && !newlyAddedFields.has(databaseField)) return;
    if (onSaveStatusChange) {
      onSaveStatusChange("loading");
    }
    const updatedInputValues = { ...inputValues, [databaseField]: newValue };
    const updatedProjectionValues = { ...projectionValues, [databaseField]: newValue };
    const updatedOutcomeValues = { ...outcomeValues, [databaseField]: newValue };

    const allInputs = {
      ...updatedInputValues,
      ...updatedProjectionValues,
      ...updatedOutcomeValues,
    };

    const formulaModeInputs = {};
    Object.entries(allInputs).forEach(([key, value]) => {
      formulaModeInputs[key] = inputMode[key] === false;
    });

    setInputValues(updatedInputValues);
    setProjectionValues(updatedProjectionValues);
    setOutcomeValues(updatedOutcomeValues);

    const updateSectionPortfolio = (sectionList, updatedValues) =>
      sectionList.map((item) => ({
        ...item,
        portfolioValue: updatedValues[item.input] ?? item.portfolioValue,
      }));

    setFormData((prev) => ({
      ...prev,
      [formKey]: {
        ...prev[formKey],
        overview: updateSectionPortfolio(overviewList, updatedInputValues),
        projection: updateSectionPortfolio(entryProjectionList, updatedProjectionValues),
        outcome: updateSectionPortfolio(entryOutcomeList, updatedOutcomeValues),
      },
    }));

    setTouchedFields((prev) => ({
      ...prev,
      [databaseField]: true,
    }));

    let section = "";
    if (overviewList.some((item) => item.input === databaseField)) section = "overview";
    else if (entryProjectionList.some((item) => item.input === databaseField)) section = "projection";
    else if (entryOutcomeList.some((item) => item.input === databaseField)) section = "outcome";

    try {
      let response;
      if (newValue !== null) {
        response = await post("/trade/calculate", {
          inputs: allInputs,
          formKey: formKey,
          locked: lockedFields,
          changedFields: databaseField,
          formulaModeInputs,
        });
      }

      const updatedFields = response?.calculated || {};

      setUpdatedCalculatedFields((prev) => ({
        ...prev,
        ...updatedFields,
      }));

      Object.entries(updatedFields).forEach(([key, value]) => {
        if (key.toLowerCase() in updatedInputValues)
          updatedInputValues[key.toLowerCase()] = value;
        if (key.toLowerCase() in updatedProjectionValues)
          updatedProjectionValues[key.toLowerCase()] = value;
        if (key.toLowerCase() in updatedOutcomeValues)
          updatedOutcomeValues[key.toLowerCase()] = value;
      });

      setInputValues(updatedInputValues);
      setProjectionValues(updatedProjectionValues);
      setOutcomeValues(updatedOutcomeValues);

      if (publishStatus === "draft" || newlyAddedFields.has(databaseField)) {
        updateDatabasePayload(formKey, {
          section,
          data: {
            ...updatedFields,
            [databaseField.toUpperCase()]: newValue,
          },
        });

        if (!isCalculation) {
          await post("/trade/save-section", {
            formKey,
            section,
            data: [{ input: databaseField, value: newValue, isFormula: inputMode[databaseField] === false }],
            formulaModeInputs,
            trade_account_id: selectedTradeAccountId
          });
        }
      }

      setRawInputs((prev) => {
        const updated = { ...prev };
        delete updated[databaseField];
        return updated;
      });

      if (onSaveStatusChange) {
        onSaveStatusChange("success");
      }
    } catch (error) {
      console.error("Error updating field:", error.response?.data || error.message);
      if (onSaveStatusChange) {
        onSaveStatusChange("error");
      }
    }
  };

  const handleMinusClick = async (itemToRemove, section) => {
    const values = section === "overview" ? inputValues : section === "projection" ? projectionValues : outcomeValues;
    setLastCalculatedValues((prev) => ({
      ...prev,
      [itemToRemove.input]:
        values[itemToRemove.input] !== undefined
          ? values[itemToRemove.input]
          : updatedCalculatedFields[itemToRemove.input.toUpperCase()] !== undefined
            ? updatedCalculatedFields[itemToRemove.input.toUpperCase()]
            : updatedCalculatedFields[itemToRemove.input.toLowerCase()] !== undefined
              ? updatedCalculatedFields[itemToRemove.input.toLowerCase()]
              : itemToRemove.portfolioValue,
    }));

    setFormData((prevFormData) => {
      const currentList = Array.isArray(prevFormData[formKey]?.[section])
        ? prevFormData[formKey][section]
        : [];

      const updatedList = currentList
        .filter((item) => item.input !== itemToRemove.input)
        .map((item) => ({
          ...item,
          portfolioValue:
            values[item.input] !== undefined
              ? values[item.input]
              : updatedCalculatedFields[item.input.toUpperCase()] !== undefined
                ? updatedCalculatedFields[item.input.toUpperCase()]
                : updatedCalculatedFields[item.input.toLowerCase()] !== undefined
                  ? updatedCalculatedFields[item.input.toLowerCase()]
                  : item.portfolioValue,
        }));

      return {
        ...prevFormData,
        [formKey]: {
          ...prevFormData[formKey],
          [section]: updatedList,
        },
      };
    });

    if (section === "overview") {
      setInputValues((prev) => {
        const updated = { ...prev };
        delete updated[itemToRemove.input];
        return updated;
      });
    } else if (section === "projection") {
      setProjectionValues((prev) => {
        const updated = { ...prev };
        delete updated[itemToRemove.input];
        return updated;
      });
    } else if (section === "outcome") {
      setOutcomeValues((prev) => {
        const updated = { ...prev };
        delete updated[itemToRemove.input];
        return updated;
      });
    }

    setRawInputs((prev) => {
      const updated = { ...prev };
      delete updated[itemToRemove.input];
      return updated;
    });

    setInputMode((prev) => {
      const updated = { ...prev };
      delete updated[itemToRemove.input];
      return updated;
    });

    setLockedFields((prev) => {
      const updated = { ...prev };
      delete updated[itemToRemove.input];
      return updated;
    });

    setTouchedFields((prev) => {
      const updated = { ...prev };
      delete updated[itemToRemove.input];
      return updated;
    });

    setNewlyAddedFields((prev) => {
      const updated = new Set(prev);
      updated.delete(itemToRemove.input);
      return updated;
    });

    updateDatabasePayload(formKey, {
      section: section,
      data: { [itemToRemove.input.toUpperCase()]: null },
    });

    await post("remove-extra-field", {
      formKey,
      section: section,
      input: itemToRemove.input,
      trade_account_id: selectedTradeAccountId,
    });
    setExtraFieldCount((prev) => ({
      ...prev,
      [section]: Math.max((prev[section] || 0) - 1, 0),
    }));
    setSelectedExtraFields((prev) => ({
      ...prev,
      [section]: (prev[section] || []).filter(
        (field) => field !== itemToRemove.input && field !== itemToRemove.database_field
      ),
    }));
    setLiveSelectedFields((prev) => ({
      ...prev,
      [section]: (prev[section] || []).filter(
        (field) => field !== itemToRemove.input && field !== itemToRemove.database_field
      ),
    }));
  };

  const handlePublishForm = async () => {
    try {
      const response = await post("/trade/publish-form", {
        formKey,
        trade_account_id: selectedTradeAccountId,
        data: {
          overview: overviewList.map((item) => ({
            input: item.input,
            value: inputValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          projection: entryProjectionList.map((item) => ({
            input: item.input,
            value: projectionValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          outcome: entryOutcomeList.map((item) => ({
            input: item.input,
            value: outcomeValues[item.input] || item.portfolioValue,
            isFormula: inputMode[item.input] === false,
          })),
          notes: notesData,
          chartUrl: chartImageUrl
        },
      });
      setPublishStatus("published");
      if (onFormPublish) onFormPublish(formKey);
      setLockedFields((prev) => {
        const newLocks = { ...prev };
        [...overviewList, ...entryProjectionList, ...entryOutcomeList].forEach((item) => {
          newLocks[item.input] = true;
        });
        return newLocks;
      });
    } catch (error) {
      console.error("Error publishing form:", error.response?.data || error.message);
      alert("Failed to publish form. Please try again.");
    }
  };

  const handleDeleteForm = async () => {
    try {
      const response = await post("/trade/delete-form", { formKey });
      if (response.success) {
        if (onDeleteForm) onDeleteForm(formKey);
      } else {
        alert("Failed to delete form. Please try again.");
      }
    } catch (error) {
      console.error("Error deleting form:", error.response?.data || error.message);
      alert("Failed to delete form. Please try again.");
    }
  };

  const handleChartUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const validTypes = ["image/jpeg", "image/png"];
    const maxSize = 5 * 1024 * 1024;
    setUploadError("");

    if (!validTypes.includes(file.type)) {
      setUploadError("Only JPG and PNG files are allowed.");
      return;
    }

    if (file.size > maxSize) {
      setUploadError("File size must be under 5MB.");
      return;
    }

    const formDataPayload = new FormData();
    formDataPayload.append("chart", file);
    formDataPayload.append("formKey", formKey);
    formDataPayload.append("tradeId", tradeId);
    formDataPayload.append("formType", "entry");

    try {
      if (onSaveStatusChange) {
        onSaveStatusChange("loading");
      }

      let chartUrl;
      if (publishStatus === "draft") {
        const response = await post("/trade/upload-chart", formDataPayload, {
          headers: { "Content-Type": "multipart/form-data" },
        });

        if (!response.success) {
          throw new Error(response.message || "Failed to upload chart.");
        }
        chartUrl = response.chartUrl;
      } else {
        chartUrl = URL.createObjectURL(file);
      }

      setChartImageUrl(chartUrl);

      setFormData((prev) => ({
        ...prev,
        [formKey]: {
          ...prev[formKey],
          chartUrl,
        },
      }));

      if (publishStatus === "draft") {
        updateDatabasePayload(formKey, {
          section: "metadata",
          data: { chartUrl },
        });
      }

      if (onSaveStatusChange) {
        onSaveStatusChange("success");
      }
    } catch (error) {
      console.error("Chart upload error:", error.response?.data || error.message);
      setUploadError("An error occurred while uploading the chart. Please try again.");
      if (onSaveStatusChange) {
        onSaveStatusChange("error");
      }
    }

    fileInputRef.current.value = "";
  };

  const filteredOverviewList = overviewList.filter((item) => !["transaction_comments", "linked_entry", "transaction_last_exit_price"].includes(item.input));
  const notesField = overviewList.find((item) => item.input === "transaction_comments");

  const renderList = useMemo(() => (list, values, section) => (
    <Row className="g-3 align-items-stretch">
      {list.map((item, index) => {
        const lowerType = item.datatype?.toLowerCase() || "";
        const isDateField = lowerType.includes("date") && !lowerType.includes("hh");
        const isTimeField = lowerType.includes("time");
        const isDateTimeField = lowerType.includes("date") && lowerType.includes("hh");
        const isReadOnly =
          !item.is_editable ||
          lockedFields[item.input] ||
          inputMode[item.input] === false ||
          item.account_field === "YES" ||
          (publishStatus === "published" && !newlyAddedFields.has(item.input));

        return (
          <Col key={item.input} md={4} sm={6} xs={12} className="d-flex">
            <div className="trade_builder_card_body_box d-flex flex-column w-100">
              <div className="head h-25 flex-grow-1 d-flex flex-column">
                <div className="d-flex align-items-center gap-2 w-100">
                  <CommonTooltip
                    className="subTooltip"
                    content={
                      <>
                        {item?.tooltip}
                        {item?.input && (
                          <>
                            {" "}
                            <a
                              href={`/education/${item.input
                                .replace(/^portfolio_|^transaction_/, "")
                                .replace(/_/g, "-")}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              style={{ color: "#00bfff", textDecoration: "underline" }}
                            >
                              Read more
                            </a>
                          </>
                        )}
                      </>
                    }
                    position="top-left"
                  >
                    <SolidInfoIcon />
                  </CommonTooltip>
                  <h5 className="w-100">{item?.title}</h5>
                  <div className="d-flex align-items-center justify-end">
                    {item.is_editable &&
                      item.has_formula &&
                      item.account_field !== "YES" &&
                      publishStatus !== "published" && (
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={inputMode[item.input] === false}
                            onChange={() => confirmToggleInputMode(item.input)}
                          />
                          <span className="slider round"></span>
                        </label>
                      )}
                    {item.show_icon && (
                      <div
                        className="d-flex align-items-center justify-content-center"
                        style={{ height: "22px", width: "25px", cursor: "pointer" }}
                        onClick={() => handleMinusClick(item, section)}
                      >
                        <MinusIcon height="15px" width="15px" />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="show_metrics_dimenstion">
                <span>
                  {item.input?.toLowerCase().startsWith("transaction_")
                    ? "TRANSACTION:"
                    : item.input?.toLowerCase().startsWith("trade_")
                      ? "TRADE:"
                      : item.input?.toLowerCase().startsWith("portfolio_")
                        ? "PORTFOLIO:"
                        : ""}
                </span>
                <span>{item.metric_dimension === "dimension" ? "DIMENSION" : "METRIC"}</span>
              </div>
              <div className="d-flex flex-column justify-content-end flex-grow-1">
                {item.is_editable &&
                  item?.expected_values?.length > 0 &&
                  item.input !== "transaction_leverage_factor" &&
                  item.account_field !== "YES" ? (
                  <select
                    value={values[item.input] || ""}
                    onChange={(e) => handleInputChange(e, item.input, item.expected_values, item.datatype)}
                    className="dropdown-select w-100"
                    style={{
                      backgroundColor: item.is_editable && publishStatus !== "published" ? "white" : "#004080",
                      color: item.is_editable && publishStatus !== "published" ? "black" : "white",
                    }}
                    disabled={isReadOnly}
                  >
                    <option value="">Select</option>
                    {item.expected_values.map((val) => (
                      <option key={val} value={val}>
                        {val}
                      </option>
                    ))}
                  </select>
                ) : isDateField ? (
                  <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                    <input
                      type="date"
                      className="date-picker flex-grow-1"
                      value={
                        rawInputs[item.input] !== undefined
                          ? rawInputs[item.input]
                          : item?.is_editable
                            ? values[item.input]?.slice(0, 10) || ""
                            : item.account_field === "YES"
                              ? item?.portfolioValue?.slice(0, 10) || ""
                              : values[item.input]?.slice(0, 10) || ""
                      }
                      style={{
                        backgroundColor: isReadOnly ? "#004080" : "white",
                        color: isReadOnly ? "white" : "black",
                        paddingRight: inputMode[item.input] === false ? "30px" : "12px",
                      }}
                      onChange={(e) => handleInputChange(e, item.input, [], item.datatype)}
                      onBlur={() => handleBlur(item.input)}
                      onFocus={() => handleFocus(item.input)}
                      disabled={isReadOnly}
                    />
                    {(inputMode[item.input] === false || item.has_formula || !item.is_editable) && (
                      <div style={{ position: "absolute", right: "10px", top: "50%", transform: "translateY(-50%)" }}>
                        <CommonTooltip
                          className="subTooltip"
                          content="💡 This field is calculated using TradeReply’s proprietary formula."
                          position="top-left"
                        >
                          <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-formula-icon.svg"
                            alt="Formula"
                            className="formula-icon"
                            style={{ width: "14px", height: "14px", opacity: 0.7 }}
                          />
                        </CommonTooltip>
                      </div>
                    )}
                  </div>
                ) : isTimeField ? (
                  <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                    <input
                      type="time"
                      step="1"
                      className="time-picker flex-grow-1"
                      value={
                        rawInputs[item.input] !== undefined
                          ? rawInputs[item.input]
                          : item?.is_editable
                            ? values[item.input] || ""
                            : item.account_field === "YES"
                              ? item?.portfolioValue || ""
                              : values[item.input] || ""
                      }
                      style={{
                        backgroundColor: isReadOnly ? "#004080" : "white",
                        color: isReadOnly ? "white" : "black",
                        paddingRight: inputMode[item.input] === false ? "30px" : "12px",
                      }}
                      onChange={(e) => handleInputChange(e, item.input, [], item.datatype)}
                      onBlur={() => handleBlur(item.input)}
                      onFocus={() => handleFocus(item.input)}
                      disabled={isReadOnly}
                    />
                    {(inputMode[item.input] === false || item.has_formula || !item.is_editable) && (
                      <div style={{ position: "absolute", right: "10px", top: "50%", transform: "translateY(-50%)" }}>
                        <CommonTooltip
                          className="subTooltip"
                          content="💡 This field is calculated using TradeReply’s proprietary formula."
                          position="top-left"
                        >
                          <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-formula-icon.svg"
                            alt="Formula"
                            className="formula-icon"
                            style={{ width: "14px", height: "14px", opacity: 0.7 }}
                          />
                        </CommonTooltip>
                      </div>
                    )}
                  </div>
                ) : isDateTimeField ? (
                  <div className="input-container position-relative d-flex flex-column">
                    <input
                      type="datetime-local"
                      step="3600"
                      className="datetime-picker flex-grow-1"
                      value={
                        rawInputs[item.input] !== undefined
                          ? rawInputs[item.input]?.replace(" ", "T").slice(0, 16)
                          : item?.is_editable
                            ? values[item.input]?.replace(" ", "T").slice(0, 16) || ""
                            : item.account_field === "YES"
                              ? item?.portfolioValue?.replace(" ", "T").slice(0, 16) || ""
                              : values[item.input]?.replace(" ", "T").slice(0, 16) || ""
                      }
                      style={{
                        backgroundColor: isReadOnly ? "#004080" : "white",
                        color: isReadOnly ? "white" : "black",
                        paddingRight: inputMode[item.input] === false ? "30px" : "12px",
                      }}
                      onChange={(e) => handleInputChange(e, item.input, [], item.datatype)}
                      onBlur={() => handleBlur(item.input)}
                      onFocus={() => handleFocus(item.input)}
                      disabled={isReadOnly}
                    />
                    {(inputMode[item.input] === false || item.has_formula || !item.is_editable) && (
                      <div style={{ position: "absolute", right: "10px", top: "50%", transform: "translateY(-50%)" }}>
                        <CommonTooltip
                          className="subTooltip"
                          content="💡 This field is calculated using TradeReply’s proprietary formula."
                          position="top-left"
                        >
                          <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-formula-icon.svg"
                            alt="Formula"
                            className="formula-icon"
                            style={{ width: "14px", height: "14px", opacity: 0.7 }}
                          />
                        </CommonTooltip>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                    <input
                      type="text"
                      className="input-field flex-grow-1"
                      value={
                        rawInputs[item.input] !== undefined
                          ? rawInputs[item.input]
                          : item?.is_editable
                            ? formatValue(values[item.input], item?.datatype, blurredFields[item.input], isFocused[item.input])
                            : item.account_field === "YES"
                              ? formatValue(item?.portfolioValue, item?.datatype, true, false)
                              : formatValue(values[item.input], item?.datatype, blurredFields[item.input], isFocused[item.input])
                      }
                      style={{
                        backgroundColor: isReadOnly ? "#004080" : inputMode[item.input] !== false ? "white" : "#004080",
                        color: isReadOnly ? "white" : inputMode[item.input] !== false ? "black" : "white",
                        paddingRight: inputMode[item.input] === false ? "30px" : "12px",
                      }}
                      onChange={(e) => handleInputChange(e, item.input, item.expected_values, item.datatype)}
                      onBlur={() => handleBlur(item.input)}
                      onFocus={() => handleFocus(item.input)}
                      readOnly={isReadOnly}
                    />
                    {(inputMode[item.input] === false || item.has_formula || !item.is_editable) && (
                      <div style={{ position: "absolute", right: "10px", top: "50%", transform: "translateY(-50%)" }}>
                        <CommonTooltip
                          className="subTooltip"
                          content="💡 This field is calculated using TradeReply’s proprietary formula."
                          position="top-left"
                        >
                          <img
                            src="https://cdn.tradereply.com/dev/site-assets/icons/tradereply-formula-icon.svg"
                            alt="Formula"
                            className="formula-icon"
                            style={{ width: "14px", height: "14px", opacity: 0.7 }}
                          />
                        </CommonTooltip>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </Col>
        );
      })}
    </Row>
  ), [blurredFields, isFocused, inputMode, lockedFields, rawInputs, publishStatus, newlyAddedFields]);

  const MAX_NOTES_LENGTH = 500;
  const notesRef = useRef(null);

  const allSections = [
    ...overviewList,
    ...entryProjectionList,
    ...entryOutcomeList,
  ];

  const getFieldValue = (inputName, fallback = "—") => {
      if (inputValues[inputName] !== undefined) return inputValues[inputName];
      if (projectionValues[inputName] !== undefined) return projectionValues[inputName];
      if (outcomeValues[inputName] !== undefined) return outcomeValues[inputName];

      const all = [
        ...(formData?.[formKey]?.overview || []),
        ...(formData?.[formKey]?.projection || []),
        ...(formData?.[formKey]?.outcome || []),
      ];
      const field = all.find(f => f.input === inputName);
      return field?.portfolioValue ?? fallback;
  };

  const lastExitValue =
  formData?.[formKey]?.overview?.find(f => f.input === "transaction_last_exit_price")?.portfolioValue;

  const formatDate = (value) => {
    if (!value) return "—";
    const d = new Date(value);
    return isNaN(d.getTime())
      ? "—"
      : d.toLocaleDateString("en-US", { month: "long", day: "numeric", year: "numeric" });
  };

  const autoResize = (ref) => {
    if (ref.current) {
      ref.current.style.height = "auto";
      ref.current.style.height = `${ref.current.scrollHeight}px`;
    }
  };

  useEffect(() => {
    autoResize(notesRef);
  }, [notesData]);

  useEffect(() => {
    const handleResize = () => {
      autoResize(notesRef);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const pl = parseFloat(getFieldValue("transaction_realized_pl_profit_loss", "0") || "0");

  const entryBoxColorClass = publishStatus === "published"
    ? pl > 0
      ? "greengrandientbg"
      : pl < 0
        ? "redgrandientbg"
        : "baseblue_bg"
    : "baseblue_bg";

  return (
    <>
      <div className={entryBoxColorClass}>
        <div className={`trade_manager_trade_entry_box ${publishStatus === "published" ? 'published' : ''}`}>
          <span className="solidArrow me-3">
            <svg width="31" height="36" viewBox="0 0 31 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M28.5 13.6699C31.8333 15.5944 31.8333 20.4056 28.5 22.3301L7.5 34.4545C4.16666 36.379 -1.74729e-06 33.9734 -1.57905e-06 30.1243L-5.19101e-07 5.87564C-3.50856e-07 2.02664 4.16667 -0.378984 7.5 1.54552L28.5 13.6699Z" fill="#FF696A">
              </path>
            </svg>
          </span>
          <div className="trade_manager_trade_entry_box_headtext w-100">
            <div className="d-flex flex-wrap gap-3">
              <h5 className="label">{`Entry ${index}`}</h5>
              <h5 className="field">
                Ticker: {getFieldValue("transaction_ticker") || '–'}
              </h5>
              <h5 className="field">
                Qty Purchased: {getFieldValue("transaction_quantity_purchased") || '–'}
              </h5>
              <h5 className="field">
                Entry Price: {getFieldValue("transaction_entry_price") ? `$${getFieldValue("transaction_entry_price")}` : '–'}
              </h5>
              <h5 className="field">
                P&L: {getFieldValue("transaction_realized_pl_profit_loss") ? `$${getFieldValue("transaction_realized_pl_profit_loss")}` : '–'}
              </h5>
            </div>
            <div className="d-flex flex-wrap gap-3 mt-1">
              <h5 className="field">
                Position Type: {getFieldValue("transaction_position_type") ? getFieldValue("transaction_position_type").toUpperCase() : '–'}
              </h5>
              <h5 className="field">
                Qty Remaining: {getFieldValue("transaction_quantity_remaining") || '–'}
              </h5>
              <h5 className="field">
                Last Exit Price: {getFieldValue("transaction_last_exit_price") ? `$${getFieldValue("transaction_last_exit_price")}` : '–'}
              </h5>
              <h5 className="field">
                Entry Date: {formatDate(getFieldValue("transaction_entry_date")) || '–'}
              </h5>
            </div>
          </div>
          {entryIsOpen && (
            <div className="trade_builder_small_btns">
              <CommonButton
                title="Delete"
                className="red-btn w-100"
                onClick={handleDeleteForm}
              />
              <CommonButton
                title={publishStatus === "published" ? "Published" : "Publish"}
                className={
                  publishStatus === "published"
                    ? "gray-btn w-100"
                    : "green-btn w-100"
                }
                onClick={publishStatus === "draft" ? handlePublishForm : undefined}
                disabled={publishStatus === "published"}
              />
            </div>
          )}
          <button
            type="button"
            className="trade_builder_card_head_btnArrow ms-3"
            onClick={toggleEntryCollapse}
          >
            {entryIsOpen ? <DropArrowUpIcon /> : <DropArrowIcon />}
          </button>
        </div>
        {entryIsOpen && (
          <div
            className="trade_builder_card_body_wrapper"
            style={{ height: height || "auto", overflow: "hidden", transition: "height 0.5s ease" }}
            ref={contentRef}
          >
            <div className="relative trade_builder_card_body_notes">
              <div className="trade_builder_card_body_box d-flex flex-column w-100 mb-1 mt-3">
                <div className="head h-25 flex-grow-1 d-flex flex-column p-2">
                  <div className="d-flex align-items-center gap-2 w-100">
                    <CommonTooltip
                      className="subTooltip"
                      content={
                        <>
                          {notesField?.summary ||
                            notesField?.tooltip ||
                            "Transaction-Level Comments capture trader notes specific to individual transactions, providing detailed context for entry and exit decisions."}
                          {notesField?.input && (
                            <>
                              {" "}
                              <a
                                href={`/education/${notesField.input.replace(/^portfolio_|^transaction_/, "").replace(/_/g, "-")}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ color: "#00bfff", textDecoration: "underline" }}
                              >
                                Read more
                              </a>
                            </>
                          )}
                        </>
                      }
                      position="top-left"
                    >
                      <SolidInfoIcon />
                    </CommonTooltip>
                    <h5 className="w-100">Comments</h5>
                  </div>
                </div>
                <div className="show_metrics_dimenstion p-2">
                  <span>TRANSACTION:</span>
                  <span>METRIC</span>
                </div>
                <div className="input-container flex-grow-1 position-relative d-flex flex-column">
                  <textarea
                    className="form-textarea w-full resize-none overflow-hidden"
                    style={{
                      backgroundColor: "#4a91c6",
                      color: "#f4F4F4",
                      fontSize: "18px",
                      fontWeight: "bold",
                      borderRadius: "none !important",
                      borderBottomLeftRadius: "10px",
                      borderBottomRightRadius: "10px",
                    }}
                    rows="4"
                    placeholder="Leave a note about this entry or exit (optional)..."
                    ref={notesRef}
                    maxLength={MAX_NOTES_LENGTH}
                    value={notesData}
                    onChange={handleNotesChange}
                    onBlur={() => {
                      if (notesData.trim() && notesData !== lastSavedNote) {
                        saveNotes(notesData);
                      }
                    }}
                  ></textarea>
                </div>
              </div>
              <div className="character-count">
                Characters left: {notesData.length}/{MAX_NOTES_LENGTH}
              </div>
            </div>
            <div className="trade_manager_btns">
              <input type="file" accept=".jpg,.jpeg,.png" style={{ display: "none" }} ref={fileInputRef} onChange={handleChartUpload} />
              <CommonButton
                title="Upload Market Chart"
                onClick={() => fileInputRef.current?.click()}
                className="w-100 mt-3"
              />
            </div>
            <div className="trade_builder_card_body">
              {renderList(filteredOverviewList, inputValues, 'overview')}
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    handleOpenModal("overview")
                  }}
                />
              </div>
              <div className="trade_builder_card_head mt-4">
                <AdminHeading heading="Overview (Projection)" centered />
              </div>
              <div className="trade_builder_card_body">
                {renderList(entryProjectionList, projectionValues, 'projection')}
              </div>
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    handleOpenModal("projection")
                  }}
                />
              </div>
              <div className="trade_builder_card_head mt-4">
                <AdminHeading heading="Overview (Post Trade)" centered />
              </div>
              <div className="trade_builder_card_body">
                {renderList(entryOutcomeList, outcomeValues, 'outcome')}
              </div>
              <div className="trade_manager_btns">
                <CommonButton
                  title="Add Metric / Dimension"
                  onlyIcon={<PlusIcon />}
                  className="w-100 mt-3"
                  onClick={() => {
                    handleOpenModal("outcome")
                  }}
                />
              </div>
            </div>
          </div>
        )}
        {showConfirmModal && (
          <div className="inset-0 z-50 modal_overlay">
            <div className="search_section h-auto">
              <h2 className="h4 font-semibold mb-4">
                Only one of these fields can be manually entered at a time.
              </h2>
              {pendingToggleField && (
                (() => {
                  const isManual = !inputMode[pendingToggleField];
                  const action = isManual ? "Manual Entry" : "Auto-Calculate";
                  const oppositeAction = isManual ? "Auto-Calculate" : "Manual Entry";
                  const fieldLabel =
                    pendingToggleField === "transaction_risk_percentage" ? "Risk Percentage" : "Quantity Purchased";
                  const otherLabel =
                    pendingToggleField === "transaction_risk_percentage" ? "Quantity Purchased" : "Risk Percentage";
                  return (
                    <p className="mb-4 h5 font-regular text-gray-600">
                      Toggling <strong>{fieldLabel}</strong> to <strong>{action}</strong> will switch <strong>{otherLabel}</strong> to{" "}
                      <strong>{oppositeAction}</strong>.
                    </p>
                  );
                })()
              )}
              <div className="flex justify-end gap-3 mt-3">
                <CommonButton title="Cancel" className="gray-btn p-2 rounded-4 lh-sm" onClick={handleCancelToggle} />
                <CommonButton title="Confirm" className="btn-primary p-2 rounded-4 lh-sm" onClick={handleConfirmToggle} />
              </div>
            </div>
          </div>
        )}
        {isDimentionModal && (
          <ConfigScopeModal
            transactionFields={transactionFields}
            tradeFields={tradeFields}
            portfolioFields={portfolioFields}
            onClose={closeDimentionModal}
            onConfirm={handleAddFields}
            selectedFieldInputs={selectedExtraFields}
            errorMessage={modalErrorMessage}
            activeSection={activeSection}
            defaultFieldInputs={defaultFields}
            limitPerSubscription={limitPerSubscription}
            onSelectionChange={handleUpdateSelectedFields}
            planName={planName}
          />
        )}
      </div>
    </>
  );
}